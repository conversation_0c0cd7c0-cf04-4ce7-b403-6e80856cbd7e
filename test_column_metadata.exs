#!/usr/bin/env elixir

# Simple test script to verify column metadata includes foreign_key and index information

Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:ecto_sqlite3, "~> 0.12"}
])

defmodule TestRepo do
  use Ecto.Repo,
    otp_app: :test_app,
    adapter: Ecto.Adapters.SQLite3
end

# Start the repo
{:ok, _} = TestRepo.start_link(database: "priv/drops_relation_sqlite_test.db")

# Load the Drops.SQL modules
Code.require_file("lib/drops/sql/database.ex")
Code.require_file("lib/drops/sql/sqlite.ex")
Code.require_file("lib/drops/sql/compilers/sqlite.ex")

alias Drops.SQL.Database

# Test the user_groups table which has foreign keys and indices
{:ok, table} = Database.table("user_groups", TestRepo)

IO.puts("Testing user_groups table column metadata:")
IO.puts("=" <> String.duplicate("=", 50))

for column <- table.columns do
  IO.puts("Column: #{column.name}")
  IO.puts("  Type: #{column.type}")
  IO.puts("  Primary Key: #{column.meta.primary_key}")
  IO.puts("  Foreign Key: #{column.meta.foreign_key}")
  IO.puts("  Index: #{column.meta.index}")
  IO.puts("  Index Name: #{inspect(column.meta.index_name)}")
  IO.puts("")
end

IO.puts("Foreign Keys:")
for fk <- table.foreign_keys do
  IO.puts("  #{inspect(fk)}")
end

IO.puts("\nIndices:")
for index <- table.indices do
  IO.puts("  #{inspect(index)}")
end
